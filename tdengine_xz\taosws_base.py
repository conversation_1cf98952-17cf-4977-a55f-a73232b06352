#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from queue import Queue, Empty
from typing import Dict, List
from contextlib import contextmanager
import taosws
import logging
logger = logging.getLogger(__name__)

class TaoswsBase:
    """TDengine WebSocket 简单封装"""
    
    def __init__(self, config: dict):
        """
        """
        protocol = config.get("database.protocol", "taosws")
        user: str = config["database.user"]
        password: str = config["database.password"]
        host: str = config["database.host"]
        port: int = config["database.port"]
        pool_size = config.get("pool_size", 5)
        self.db_url = f"{protocol}://{user}:{password}@{host}:{port}"
        self.pool_size = pool_size
        # 连接池
        self.conn_pool = Queue()
        # 初始化连接池
        self.init_conn_pool(pool_size)
    
    def create_connect(self, db_url=None):
        if not db_url:
            db_url = self.db_url
        conn = taosws.connect(self.db_url)
        return conn

    def init_conn_pool(self, size=None):
        """初始化连接池"""
        if not size:
            size = self.pool_size
        for _ in range(size):
            conn = self.create_connect()
            if conn:
                self.conn_pool.put(conn)
                
    def get_conn(self):
        conn = self.conn_pool.get(timeout=0.1)
        return conn

    @contextmanager       
    def connection(self):
        """获取连接"""
        conn = None
        try:
            # 从池中获取连接
            conn = self.conn_pool.get(timeout=0.1)
            yield conn
        finally:
            # 归还连接
            self.conn_pool.put(conn, timeout=1)
    
    def conn_execute(self, sql):
        with self.connection() as conn:
            return conn.execute(sql) 
    def conn_query(self, sql):
        with self.connection() as conn:
            result = conn.query(sql)
            return result
    
    def query(self, sql):
        with self.connection() as conn:
            resut = conn.query(sql)
            # print(r.fields)

            return resut
        
    def cursor_execute(self, sql):
        with self.connection() as conn:
            cursor = conn.cursor()
            res = cursor.execute(sql)
            cursor.close()
            return res
    
    def execute_many(self, sql, data: List[Dict]=None, db_name=None):
        # 效率不高
        with self.connection() as conn:
            cursor = conn.cursor()
            if db_name:
                cursor.execute(f"use {db_name}")
            res = None
            if data:
                res = cursor.execute_many(sql, data)
            else:
                res = cursor.execute_many(sql)
            cursor.close()
            return res
    
    def fetchone(self, sql, db_name=None, is_dict=False):
        with self.connection() as conn:
            cursor = conn.cursor()
            if db_name:
                cursor.execute("use {name}", name=db_name)
            cursor.execute(sql)
            if is_dict:
                row = cursor.fetch_all_into_dict()
            else:
                row = cursor.fetchone()
            cursor.close()
            return row 
        
    def fetchall(self, sql, db_name=None, is_dict=False):
        with self.connection() as conn:
            cursor = conn.cursor()
            if db_name:
                cursor.execute("use {name}", name=db_name)
            cursor.execute(sql)
            if is_dict:
                row = cursor.fetch_all_into_dict()
            else:
                row = cursor.fetchall()
            cursor.close()
            return row 

    def drop_table(self, table_name: str, is_super=False):
        """删除表"""
        if is_super:
            sql = f"DROP STABLE IF EXISTS {table_name}"
        else:
            sql = f"DROP TABLE IF EXISTS {table_name}"
        with self.connection() as conn:
            conn.execute(sql)
        logger.warning(f"删除表 {table_name}")

    def drop_db(self, db_name):
        sql = f"DROP DATABASE {db_name};"
        self.conn_execute(sql)

    def close(self):
        """关闭客户端"""
        # 关闭所有连接
        while not self.conn_pool.empty():
            try:
                conn = self.conn_pool.get_nowait()
                conn.close()
            except:
                pass
        
        logger.info("TDengine 客户端已关闭")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

