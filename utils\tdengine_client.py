#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue, Empty
from typing import Dict, List, Any, Optional, Union
from contextlib import contextmanager
import taosws
import logging
import pandas as pd
import datetime
import warnings
logger = logging.getLogger(__name__)

class TaoswsDatabase:
    """TDengine WebSocket 简单高效客户端"""
    
    def __init__(self, config: dict):
        """
        """
        db_protocol = config.get("db_protocol", "taosws")
        db_user = config.get("db_user", "root")
        db_pass = config.get("db_pass", "taosdata")
        db_host = config.get("db_host", "localhost")
        db_port = config.get("db_port", "6041")
        pool_size = config.get("pool_size", 5)
        self.db_url = f"{db_protocol}://{db_user}:{db_pass}@{db_host}:{db_port}"
        self.pool_size = pool_size
        # 连接池
        self.conn_pool = Queue()
        # 初始化连接池
        self.init_conn_pool(pool_size)
    
    def create_connect(self, db_url=None):
        if not db_url:
            db_url = self.db_url
        conn = taosws.connect(self.db_url)
        return conn

    def init_conn_pool(self, size=None):
        """初始化连接池"""
        if not size:
            size = self.pool_size
        for _ in range(size):
            conn = self.create_connect()
            if conn:
                self.conn_pool.put(conn)

    @contextmanager       
    def get_connection(self):
        """获取连接"""
        conn = None
        try:
            # 从池中获取连接
            conn = self.conn_pool.get(timeout=0.1)
            yield conn
        finally:
            # 归还连接
            self.conn_pool.put(conn, timeout=1)
    
    def conn_execute(self, sql):
        with self.get_connection() as conn:
            return conn.execute(sql) 
    def conn_query(self, sql):
        with self.get_connection() as conn:
            result = conn.query(sql)
            return result
    
    def query(self, sql):
        with self.get_connection() as conn:
            resut = conn.query(sql)
            # print(r.fields)

            return resut
        
    def cursor_execute(self, sql):
        with self.get_connection() as conn:
            cursor = conn.cursor()
            res = cursor.execute(sql)
            cursor.close()
            return res
    
    def execute_many(self, sql, data: List[Dict]=None, db_name=None):
        # 效率不高
        with self.get_connection() as conn:
            cursor = conn.cursor()
            if db_name:
                cursor.execute(f"use {db_name}")
            res = None
            if data:
                res = cursor.execute_many(sql, data)
            else:
                res = cursor.execute_many(sql)
            cursor.close()
            return res
    
    def fetchone(self, sql, db_name=None, is_dict=False):
        with self.get_connection() as conn:
            cursor = conn.cursor()
            if db_name:
                cursor.execute("use {name}", name=db_name)
            cursor.execute(sql)
            if is_dict:
                row = cursor.fetch_all_into_dict()
            else:
                row = cursor.fetchone()
            cursor.close()
            return row 
        
    def fetchall(self, sql, db_name=None, is_dict=False):
        with self.get_connection() as conn:
            cursor = conn.cursor()
            if db_name:
                cursor.execute("use {name}", name=db_name)
            cursor.execute(sql)
            if is_dict:
                row = cursor.fetch_all_into_dict()
            else:
                row = cursor.fetchall()
            cursor.close()
            return row 

    def drop_table(self, table_name: str, is_super=False):
        """删除表"""
        if is_super:
            sql = f"DROP STABLE IF EXISTS {table_name}"
        else:
            sql = f"DROP TABLE IF EXISTS {table_name}"
        with self.get_connection() as conn:
            conn.execute(sql)
        logger.warning(f"删除表 {table_name}")

    def drop_db(self, db_name):
        sql = f"DROP DATABASE {db_name};"
        self.conn_execute(sql)

    def close(self):
        """关闭客户端"""
        # 关闭所有连接
        while not self.conn_pool.empty():
            try:
                conn = self.conn_pool.get_nowait()
                conn.close()
            except:
                pass
        
        logger.info("TDengine 客户端已关闭")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


class TDClient(TaoswsDatabase):
    def __init__(self, config: dict):
        self.period_map = {
            "1d": "day",
            "1m": "minute",
            "1t": "tick",
            "day": "day",
            "minute": "minute",
            "tick": "tick"
        }
        super().__init__(config)
    def init_db(self):
        sql_list = [
            "CREATE DATABASE IF NOT EXISTS stock_info KEEP 365000",
            "CREATE DATABASE IF NOT EXISTS histor KEEP 365000",

        ]
        for sql in sql_list:
            self.conn_execute(sql)
    
    def create_table(self, table_name: str, columns: Dict[str, str], tags: Dict[str, str] = None, db_name=None):
        """
        创建表
        Args:
            table_name: 表名
            columns: 列定义 {"列名": "类型"}
            tags: 标签定义（超级表用）
        """
        # 构建列定义
        col_defs = [f"{name} {type_}" for name, type_ in columns.items()]
        if tags:
            # 创建超级表
            tag_defs = [f"{name} {type_}" for name, type_ in tags.items()]
            sql = f"CREATE STABLE IF NOT EXISTS {db_name}.{table_name} ({','.join(col_defs)}) TAGS ({','.join(tag_defs)})"
        else:
            sql = f"CREATE TABLE IF NOT EXISTS {db_name}.{table_name} ({','.join(col_defs)})"
        res = self.conn_execute(sql)
        logger.info(f"创建表 {table_name} {res}")

    def create_history_tables(self, code_list, period='1d', db_name=None):
        data = []
        period = self.period_map[period]
        for stock_code in code_list:
            code, market = stock_code.split(".")
            table_name = f"{period}_{code}_{market.lower()}"
            if db_name:
                table_name = f"{db_name}.{table_name}"
            data.append({
                "name": table_name,
                "super_name": period,
                "stock_code": stock_code,
                "code": code,
                "market": market,
                })
            
        sql = "CREATE TABLE IF NOT EXISTS {name} USING {super_name} TAGS ('{stock_code}','{code}','{market}')"
        # print(sql,  data)
        res = self.execute_many(sql, data=data, db_name=db_name)
        logger.info(f"创建表 {table_name} {res}")

    def stmt2_insert_histor(self, stock_code, df, period='1d', db_name='history'):

        keys = df.keys()
        batch_size = 5000
        period = self.period_map[period]
        # keys = df.columns.tolist()
        table_name=f"{db_name}.{period}_{stock_code.replace('.', '_').lower()}"
        sql = f"insert into {table_name} values ({','.join(['?' for _ in keys])})"
        conn = self.conn_pool.get(timeout=0.2)
        count = 0
        stmt2 = conn.stmt2_statement()
        stmt2.prepare(sql)
        # print(df["datatime"])
        columns = [
            taosws.millis_timestamps_to_column(df[k])
            if k=='datatime' 
            else taosws.doubles_to_column(df[k])
            for k in keys
            ]
        pyStmt2Param = taosws.stmt2_bind_param_view(
            table_name="",
            tags=None,
            columns=columns
        )
        stmt2.bind([pyStmt2Param])
        count = stmt2.execute()

        # total_rows = len(df)
        # code, market = stock_code.split(".")
        # table_name = f"{period}_{code}_{market.lower()}"
        # for start in range(0, total_rows, batch_size):
        #     end = min(start + batch_size, total_rows)
        #     df_batch = df.iloc[start:end]
        #     columns = [
        #         taosws.millis_timestamps_to_column(df_batch[k].tolist())
        #         if k=='datatime' 
        #         else taosws.doubles_to_column(df_batch[k].tolist())
        #         for k in keys
        #         ]
        #     pyStmt2Param = taosws.stmt2_bind_param_view(
        #         table_name="",
        #         tags=None,
        #         columns=columns
        #     )
        #     stmt2.bind([pyStmt2Param])
        #     count += stmt2.execute()
        stmt2.close()
        self.conn_pool.put(conn)
        return count
    # 单表tick数据 normal
    def stmt2_real_tick(self, data: dict, period='tick', db_name='real_data'):
        period = self.period_map[period]
        keys = ["datatime","stock_code",
                 "last_price", "open", "high", "low","pre_close", "volume", "amount",
                # "bid_price_1", "bid_price_2", "bid_price_3", "bid_price_4", "bid_price_5",
                # "ask_price_1", "ask_price_2", "ask_price_3", "ask_price_4","ask_price_5",
                # "bid_volume_1", "bid_volume_2", "bid_volume_3", "bid_volume_4", "bid_volume_5",
                # "ask_volume_1", "ask_volume_2", "ask_volume_3", "ask_volume_4", "ask_volume_5",
                
                ]
        # data_info = {k:[] for k in keys}
        data_info = {}
        for stock_code, dct in data.items():
            for k, v in dct.items():
                # if k not in ("datetime", "stock_code"):
                #     v = float(v)
                data_info.setdefault(k, []).append(v)
            data_info.setdefault("stock_code", []).append(stock_code)

        sql = f"insert into {db_name}.{period} values ({','.join(['?' for _ in keys])})"
        conn = self.conn_pool.get(timeout=0.2)
        # conn.execute(f"use {db_name}")
        print(sql)
        stmt2 = conn.stmt2_statement()
        stmt2.prepare(sql)
        bind_param = []
        columns = []
        print(dct)
        for k in keys:
            if k=='datatime':
                columns.append(
                  taosws.millis_timestamps_to_column(data_info[k])   
                )
            elif k=='stock_code':
                columns.append(
                  taosws.varchar_to_column(data_info[k])   
                )
            else:
                columns.append(
                  taosws.doubles_to_column(data_info[k])   
                )
        
        stmt2param = taosws.stmt2_bind_param_view(
            table_name="", 
            tags=None,
            columns=columns
        )
            
        stmt2.bind([stmt2param])
        count = stmt2.execute()
        stmt2.close()
        self.conn_pool.put(conn)

        return count

    # 多表tick数据
    def stmt2_real_tick_stable(self, data: dict, period='tick', db_name='real_data'):
        period = self.period_map[period]
        keys = ["datatime", "last_price", "open", "high", "low","pre_close", "volume", "amount",
                "bid_price_1", "bid_price_2", "bid_price_3", "bid_price_4", "bid_price_5",
                "ask_price_1", "ask_price_2", "ask_price_3", "ask_price_4","ask_price_5",
                "bid_volume_1", "bid_volume_2", "bid_volume_3", "bid_volume_4", "bid_volume_5",
                "ask_volume_1", "ask_volume_2", "ask_volume_3", "ask_volume_4", "ask_volume_5"
                ]
        sql = f"insert into ? USING tick tags (?, ?, ?) values ({','.join(['?' for _ in keys])})"
        conn = self.conn_pool.get(timeout=0.2)
        conn.execute(f"use {db_name}")
        count = 0
        stmt2 = conn.stmt2_statement()
        stmt2.prepare(sql)
        bind_param = []
        for stock_code, dct in data.items():
            code, market = stock_code.split(".")
            table_name = f"{period}_{code}_{market.lower()}"
            columns = [
                taosws.millis_timestamps_to_column([dct[k]]) 
                if k=='datetime' 
                else taosws.doubles_to_column([dct.get(k, 0.1)])
                for k in keys
                ]
            
            stmt2param = taosws.stmt2_bind_param_view(
                table_name=f"{table_name}", 
                tags=[
                    taosws.varchar_to_tag(stock_code),
                    taosws.varchar_to_tag(code),
                    taosws.varchar_to_tag(market),
                ],
                columns=columns
            )
            bind_param.append(stmt2param)
            
            break
        stmt2.bind(bind_param)
        count += stmt2.execute()
        stmt2.close()
        self.conn_pool.put(conn)

        return count


    
    def format_time_full(self, time_str: str) -> str:
        # 尝试匹配 "YYYY-MM-DD" 格式
        try:
            dt = datetime.datetime.strptime(time_str, "%Y-%m-%d")
            return dt.strftime("%Y-%m-%d %H:%M:00.000").replace("%H:%M", "00:00")
        except ValueError:
            pass
        # 尝试匹配 "YYYY-MM-DD HH:mm" 格式
        try:
            dt = datetime.datetime.strptime(time_str, "%Y-%m-%d %H:%M")
            return dt.strftime("%Y-%m-%d %H:%M:00.000")
        except ValueError:
            pass
        # 尝试匹配最完整的时间格式（包含毫秒）
        try:
            dt = datetime.datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
            return dt.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]  # 只保留三位毫秒
        except ValueError:
            raise ValueError(f"Unsupported time format: {time_str}")

    def pd_read_sql(self, sql):
        with self.get_connection() as conn:
            df: pd.DataFrame = pd.read_sql(sql, conn)
            return df
    
    def get_market_data_ex(self, field_list=[], stock_list=[], period='1d', start_time='', end_time='', count=-1, dividend_type='none'):
        if period not in self.period_map:
            raise ValueError(f"不支持的时间周期: {period}")
        if not stock_list:
            raise ValueError("股票列表不能为空")
        period = self.period_map[period]
        where = ""
        if field_list:
            fields = ",".join(field_list)
        else:
            fields = "*"
        if start_time:
            where += f"AND datatime >= '{self.format_time_full(start_time)}' "
        if end_time:
            where += f"AND datatime <= '{self.format_time_full(end_time)}' "
        if count > 0:
            where += f"LIMIT {count} "
        tmp_results = {}
        database = "stock_data"
        for symbol_vt in stock_list:
            table_name = f"{database}.{period}_{symbol_vt.replace('.', '_').lower()}"
            sql = f"SELECT {fields} FROM {table_name} {where}"
            tmp_results[symbol_vt] = self.pd_read_sql_async(sql)
        data = {}
        for symbol_vt, future in tmp_results.items():
            data[symbol_vt] = future.result()
        return data

    def init_history_stable(self):
        db_name = "history"
        dct = {
            "tick": ["datatime",
                "last_price", "open", "high", "low", "pre_close", "volume", "amount",
                "bid_price_1", "bid_price_2", "bid_price_3", "bid_price_4", "bid_price_5",
                "ask_price_1", "ask_price_2", "ask_price_3", "ask_price_4","ask_price_5",
                "bid_volume_1", "bid_volume_2", "bid_volume_3", "bid_volume_4", "bid_volume_5",
                "ask_volume_1", "ask_volume_2", "ask_volume_3", "ask_volume_4", "ask_volume_5",
            ],
            # "mine": [],
            # "day": [],
        }
        tags = {
            "stock_code": "BINARY(20)",
            "code": "BINARY(10)",
            "market": "BINARY(10)",
        }
        for period, col_keys in dct.items():
            columns = {}
            for x in col_keys:
                if x == "datatime":
                    columns[x] = "TIMESTAMP"
                elif x == "stock_code":
                    columns[x] = "BINARY(20) COMPOSITE KEY"
                else:
                    columns[x] = "DOUBLE"
            self.create_table(period, columns=columns, tags=tags, db_name=db_name)

if __name__ == "__main__":
    # 使用示例
    env = {
        'db_protocol': 'taosws',
        'db_user': "root",
        'db_pass': "taosdata",
        'db_host': "**************",
        'db_port': 6041,
        'db_name': "histor",
    }
    client = TDClient(env)

