#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue, Empty
from typing import Dict, List, Any, Optional, Union
from contextlib import contextmanager
import taosws
import logging
import pandas as pd
import datetime
import warnings
#  忽略 pandas 关于 SQLAlchemy 的警告
warnings.filterwarnings("ignore", message=".*pandas only supports SQLAlchemy.*")
logger = logging.getLogger(__name__)

class TaoswsDatabase:
    """TDengine WebSocket 简单高效客户端"""
    
    def __init__(self, host="localhost", port=6041, user="root", password="taosdata", 
                 pool_size=10):
        """
        初始化客户端
        Args:
            host: TDengine 服务器地址
            port: WebSocket 端口
            user: 用户名
            password: 密码
            pool_size: 连接池大小
        """
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.pool_size = pool_size
        
        # 连接池
        self._pool = Queue(maxsize=pool_size)
        self._lock = threading.Lock()
        self._closed = False
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 初始化连接池
        self._init_pool()
        
        print(f"✅ TDengine 客户端初始化完成 (连接池: {pool_size}, 工作线程: {max_workers})")
    
    def _init_pool(self):
        """初始化连接池"""
        info = dict(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
        )
        if self.default_database:
            info["database"] = self.default_database
        for _ in range(self.pool_size):
            try:
                conn = taosws.connect(
                    **info
                )
                self._pool.put(conn)
            except Exception as e:
                print(f"⚠️ 创建连接失败: {e}")
    
    @contextmanager
    def _get_connection(self, database=None):
        """获取连接"""
        if self._closed:
            raise Exception("客户端已关闭")
        conn = None
        try:
            # 从池中获取连接
            conn = self._pool.get(timeout=30)
            # 切换数据库
            # if database and database != self.default_database:
                # conn.select_db(f"USE {database};")
            yield conn
            
        finally:
            # 归还连接
            try:
                # if database and self.default_database and database != self.default_database:
                    # conn.execute(f"USE {self.default_database}")
                self._pool.put(conn, timeout=1)
            except:
                try:
                    conn.close()
                except:
                    pass

    
    def query(self, sql: str, database=None) -> List[Dict[str, Any]]:
        """
        执行查询
        
        Args:
            sql: SQL 语句
            database: 数据库名（可选）
            
        Returns:
            查询结果列表
        """
        with self._get_connection(database) as conn:
            cursor = conn.cursor()
            cursor.execute(sql)
            # 获取列名
            # columns = [desc[0] for desc in cursor.description] if cursor.description else []
            # 获取数据
            rows = cursor.fetchall()
            cursor.close()
            # # 转换为字典列表
            # result = []
            # for row in rows:
            #     result.append(dict(zip(columns, row)))
            
            return rows
    
    def execute(self, sql: str, database=None) -> int:
        """
        执行 SQL（INSERT/UPDATE/DELETE）
        
        Args:
            sql: SQL 语句
            database: 数据库名（可选）
            
        Returns:
            影响的行数
        """
        with self._get_connection(database) as conn:
            cursor = conn.cursor()
            cursor.execute(sql)
            affected_rows = cursor.row_count
            cursor.close()
            return affected_rows
    
    def query_async(self, sql: str, database=None, wait=True):
        """
        查询放线程池执行
        Args:
            sql: SQL 语句
            database: 数据库名（可选）
            wait: 是否等待结果
            
        Returns:
            如果 wait=True 返回结果，否则返回 Future 对象
        """
        future = self.executor.submit(self.query, sql, database)
        
        if wait:
            return future.result()
        else:
            return future
    
    def execute_async(self, sql: str, database=None, wait=True):
        """
        放线程池执行
        Args:
            sql: SQL 语句
            database: 数据库名（可选）
            wait: 是否等待结果
        Returns:
            如果 wait=True 返回影响行数，否则返回 Future 对象
        """
        future = self.executor.submit(self.execute, sql, database)
        
        if wait:
            return future.result()
        else:
            return future
    
    def batch_query(self, sql_list: List[str], database=None, wait=True):
        """
        批量查询
        
        Args:
            sql_list: SQL 语句列表
            database: 数据库名（可选）
            wait: 是否等待所有结果
            
        Returns:
            如果 wait=True 返回结果列表，否则返回 Future 列表
        """
        futures = []
        for sql in sql_list:
            future = self.executor.submit(self.query, sql, database)
            futures.append(future)
        
        if wait:
            results = []
            for future in as_completed(futures):
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    print(f"❌ 查询失败: {e}")
                    results.append(None)
            return results
        else:
            return futures
    
    def batch_execute(self, sql_list: List[str], database=None, wait=True):
        """
        批量执行
        
        Args:
            sql_list: SQL 语句列表
            database: 数据库名（可选）
            wait: 是否等待所有结果
            
        Returns:
            如果 wait=True 返回影响行数列表，否则返回 Future 列表
        """
        futures = []
        for sql in sql_list:
            future = self.executor.submit(self.execute, sql, database)
            futures.append(future)
        
        if wait:
            results = []
            for future in as_completed(futures):
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    print(f"❌ 执行失败: {e}")
                    results.append(0)
            return results
        else:
            return futures
    
    def query_async_df(self, sql: str, database=None, wait=True):
        def read_sql(sql, database=None):
            with self._get_connection(database) as conn:
                
                df: pd.DataFrame = pd.read_sql(sql, conn)
                return df
        future = self.executor.submit(read_sql, sql, database)
        if wait:
            return future.result()
        else:
            return future
    
    def close(self):
        """关闭客户端"""
        self._closed = True
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        # 关闭所有连接
        while not self._pool.empty():
            try:
                conn = self._pool.get_nowait()
                conn.close()
            except:
                pass
        
        print("🔌 TDengine 客户端已关闭")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


class TDClient(TaoswsDatabase):
    def __init__(self, host="localhost", port=6041, user="root", password="taosdata",
                  database=None, pool_size=10, max_workers=5):
        self.database = database
        self.period_map = {
            "1d": "day",
            "1m": "minute",
            "1t": "tick",
            "day": "day",
            "minute": "minute",
            "tick": "tick"
        }
        super().__init__(host, port, user, password, database, pool_size, max_workers)
    
    def create_table(self, table_name: str, columns: Dict[str, str], tags: Dict[str, str] = None):
        """
        创建表
        
        Args:
            table_name: 表名
            columns: 列定义 {"列名": "类型"}
            tags: 标签定义（超级表用）
        """
        # 构建列定义
        col_defs = [f"{name} {type_}" for name, type_ in columns.items()]
        
        if tags:
            # 创建超级表
            tag_defs = [f"{name} {type_}" for name, type_ in tags.items()]
            sql = f"CREATE STABLE IF NOT EXISTS {table_name} ({','.join(col_defs)}) TAGS ({','.join(tag_defs)})"
        else:
            sql = f"CREATE TABLE IF NOT EXISTS {table_name} ({','.join(col_defs)})"
        with self._get_connection() as conn:
            conn.execute(sql)
        logger.info(f"创建表 {table_name}")

    def create_table_use_super(self, table_name: str, super_table_name: str, tags: Dict[str, str]):
        """使用超级表创建表"""
        tag_defs = [f"{name}={value}" for name, value in tags.items()]
        sql = f"CREATE TABLE IF NOT EXISTS {table_name} USING {super_table_name} TAGS ({','.join(tag_defs)})"
        with self._get_connection() as conn:
            conn.execute(sql)
        logger.info(f"使用超级表{super_table_name} 创建表 {table_name}")

    def drop_table(self, table_name: str, is_super=False):
        """删除表"""
        if is_super:
            sql = f"DROP STABLE IF EXISTS {table_name}"
        else:
            sql = f"DROP TABLE IF EXISTS {table_name}"
        with self._get_connection() as conn:
            conn.execute(sql)
        logger.warning(f"删除表 {table_name}")
    
    def format_time_full(self, time_str: str) -> str:
        # 尝试匹配 "YYYY-MM-DD" 格式
        try:
            dt = datetime.datetime.strptime(time_str, "%Y-%m-%d")
            return dt.strftime("%Y-%m-%d %H:%M:00.000").replace("%H:%M", "00:00")
        except ValueError:
            pass
        # 尝试匹配 "YYYY-MM-DD HH:mm" 格式
        try:
            dt = datetime.datetime.strptime(time_str, "%Y-%m-%d %H:%M")
            return dt.strftime("%Y-%m-%d %H:%M:00.000")
        except ValueError:
            pass
        # 尝试匹配最完整的时间格式（包含毫秒）
        try:
            dt = datetime.datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
            return dt.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]  # 只保留三位毫秒
        except ValueError:
            raise ValueError(f"Unsupported time format: {time_str}")


    def get_market_data_ex(self, field_list=[], stock_list=[], period='1d', start_time='', end_time='', count=-1, dividend_type='none'):
        if period not in self.period_map:
            raise ValueError(f"不支持的时间周期: {period}")
        if not stock_list:
            raise ValueError("股票列表不能为空")
        period = self.period_map[period]
        where = ""
        if field_list:
            fields = ",".join(field_list)
        else:
            fields = "*"
        if start_time:
            where += f"AND datatime >= '{self.format_time_full(start_time)}' "
        if end_time:
            where += f"AND datatime <= '{self.format_time_full(end_time)}' "
        if count > 0:
            where += f"LIMIT {count} "
        tmp_results = {}
        database = "stock_data"
        for symbol_vt in stock_list:
            table_name = f"{database}.{period}_{symbol_vt.replace('.', '_').lower()}"
            sql = f"SELECT {fields} FROM {table_name} {where}"
            
            tmp_results[symbol_vt] = self.query_async_df(sql, database=database, wait=False)
        data = {}
        for symbol_vt, future in tmp_results.items():
            data[symbol_vt] = future.result()
        return data


    
# 便捷函数
def quick_connect(host="localhost", port=6041, user="root", password="taosdata", 
                 database=None, pool_size=10, max_workers=5):
    """快速连接"""
    return 


if __name__ == "__main__":
    # 使用示例
    print("🚀 TDengine WebSocket 简单高效客户端示例")
    client = TDClient(
        host="**************",
        port=6041,
        user="root",
        password="taosdata",
        # database="stock",
        pool_size=10,
        max_workers=5
    )
    sql = "SELECT * FROM day_000001_sz"
    # d =  client.execute(sql, database="stock_data")
    d = client.get_market_data_ex(stock_list=["000001.SZ"])
    print(d)
    
