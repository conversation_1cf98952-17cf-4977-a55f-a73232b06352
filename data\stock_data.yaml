# 时间格式为 YYYY-MM-DD HH:mm:ss.MS，默认时间分辨率为毫秒。比如：2017-08-12 18:25:58.128
stock:
  # 数据库
  db_name: stock
  table_info:
    # 超级表
    - table_name: day
      columns:
        datatime: TIMESTAMP
        open: DOUBLE
        high: DOUBLE
        low: DOUBLE
        close: DOUBLE
        pre_close: DOUBLE
        volume: DOUBLE
        amount: DOUBLE
      tags:
        symbol: NCHAR(10)
        exchange: NCHAR(10)
    - table_name: minute
      columns:
        datatime: TIMESTAMP
        open: DOUBLE
        high: DOUBLE
        low: DOUBLE
        close: DOUBLE
        pre_close: DOUBLE
        volume: DOUBLE
        amount: DOUBLE
      tags:
        symbol: NCHAR(10)
        exchange: NCHAR(10)
    - table_name: tick
      columns:
        datatime: TIMESTAMP
        last_price: DOUBLE
        open: DOUBLE
        high: DOUBLE
        low: DOUBLE
        close: DOUBLE
        pre_close: DOUBLE
        volume: DOUBLE
        amount: DOUBLE
        bid_price_1: DOUBLE
        bid_price_2: DOUBLE
        bid_price_3: DOUBLE
        bid_price_4: DOUBLE
        bid_price_5: DOUBLE
        bid_price_5: DOUBLE
        ask_price_1: DOUBLE
        ask_price_2: DOUBLE
        ask_price_3: DOUBLE
        ask_price_4: DOUBLE
        ask_price_5: DOUBLE
        bid_volume_1: DOUBLE
        bid_volume_2: DOUBLE
        bid_volume_3: DOUBLE
        bid_volume_4: DOUBLE
        bid_volume_5: DOUBLE
        ask_volume_1: DOUBLE
        ask_volume_2: DOUBLE
        ask_volume_3: DOUBLE
        ask_volume_4: DOUBLE
        ask_volume_5: DOUBLE
      tags:
        symbol: NCHAR(10)
        exchange: NCHAR(10)


