# from tdengine_xz.taosws_database import TaoswsDatabase

# db_client = TaoswsDatabase()
# tag_info = {
#     ""
# }
# sql_lst = [
#     # "ALTER STABLE history.day RENAME  TAG stock_code vt_symbol",
#     # "ALTER STABLE history.day RENAME  TAG code symbol",
#     # "ALTER STABLE history.day RENAME  TAG market exchange",
#     "ALTER STABLE history.minute RENAME  TAG stock_code vt_symbol",
#     "ALTER STABLE history.minute RENAME  TAG code symbol",
#     "ALTER STABLE history.minute RENAME  TAG market exchange",
# ]
# # minute_980022_sz
# with db_client.connection() as conn:
#     # for sql in sql_lst:
#     #     conn.execute(sql)
#     lst = ["vt_symbol BINARY(20)", "symbol BINARY(20)", "exchange BINARY(10)"]
#     for x in lst:
#         for t in ("day", "minute"):
#             sql = f"ALTER STABLE history.{t} MODIFY TAG {x}"
#             conn.execute(sql)

import datetime
# 定义目标时间
d  = {
    "a": 1,
    "b": 2,
    "c": 3,
}
for idx, k in enumerate(d):
    print(idx, k)