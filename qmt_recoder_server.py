import time
import datetime
import copy
from queue import Empty, Queue
from threading import Thread
from tdengine_xz.taosws_database import TaoswsDatabase
from xtquant import xtdata
from vnpy_xz.trader.object import TickData, BarData
from vnpy_xz.trader.utility import BarGenerator, generate_datetime
from vnpy_xz.trader.constant import Exchange, Interval, TickDirection

class RecodeServer:
    def __init__(self):
        self._active = False
        self.recoder_tick_queue = Queue()
        self.other_tick_queue = Queue()
        self.vt_symbols: list[str] = []
        self.bgs: dict[str, BarGenerator] = {}
        self.full_tick = {}
        self.last_tick_time = None
        self.thread_task = [
           Thread(target=self.recoder_tick),
           Thread(target=self.handle_tick)
        ]
        self.db_client = TaoswsDatabase()
        self.init()
  

    def init(self):
        self.db_client.init_db()
        self.vt_symbols = self.get_stock_list()
        # 绑定合约代码
        for vt_symbol in self.vt_symbols:
            def on_bar(bar: BarData) -> None:
                """"""
                pass
            self.bgs[vt_symbol] = BarGenerator(on_bar)
    def recoder_tick(self):
        while self._active:
            try:
                ticks = self.recoder_tick_queue.get(block=True, timeout=1)
                self.db_client.save_tick_data(ticks=ticks)
            except Empty:
                pass

    def handle_tick(self):
        tick: TickData = None
        # 定义目标时间
        time_9_25 = datetime.time(9, 25)
        time_11_30 = datetime.time(11, 30)
        time_13_00 = datetime.time(13, 0)
        time_15_00 = datetime.time(15, 0)
        is_run = False
        while self._active:
            try:
                ticks = self.other_tick_queue.get(block=True, timeout=1)
                # 合成分钟数据
                for tick in ticks:
                    t_time = tick.datetime.time() 
                    if t_time < time_9_25 or t_time == time_13_00:
                        self.last_tick_time = tick.datetime
                        continue
                    if t_time == time_9_25:
                        tick = copy.deepcopy(tick)
                        tick.datetime = tick.datetime.replace(minute=29)
                        is_run = True
                    elif t_time in (time_11_30, time_15_00):
                        tick = copy.deepcopy(tick)
                        tick.datetime = tick.datetime - datetime.timedelta(milliseconds=10)
                        is_run = True
                    if (
                        self.last_tick_time
                        and self.last_tick_time.minute != tick.datetime.minute
                    ):
                        bars = {}
                        for vt_symbol, bg in self.bgs.items():
                            bars[vt_symbol] = bg.generate()
                        self.last_tick_time = tick.datetime

                        self.db_client.save_bar_data(
                            bars=[bar for bar in bars.values() if bar], interval=Interval.MINUTE
                            )
                    bg = self.bgs[tick.vt_symbol]
                    bg.update_tick(tick)
                # 处理几个特殊时间
                if is_run:
                    bars = {}
                    for vt_symbol, bg in self.bgs.items():
                        bars[vt_symbol] = bg.generate()
                    self.last_tick_time = tick.datetime
                    self.db_client.save_bar_data(
                        bars=[bar for bar in bars.values() if bar], interval=Interval.MINUTE
                        )
                    is_run = False

            except Empty:
                pass
    

    def get_stock_list(self):
        lst = [
            "沪深A股",
            "沪深ETF",
            # "沪深基金",
            # "沪深指数",
            "沪深转债",
        ]
        stock_list = []
        for k in lst:
            stock_list.extend(xtdata.get_stock_list_in_sector(k)) 
        return stock_list
    
    def extarp_tick(self, tick: TickData):
        vt_symbol = tick.vt_symbol
        pre_tick: TickData =  self.full_tick.get(vt_symbol)
        if (
            (not pre_tick) or
            (not pre_tick.bid_price_1 and not pre_tick.ask_price_1) or
            (not tick.bid_price_1 and not tick.ask_price_1)
        ):
            return 
        tick.net_amount = tick.amount - pre_tick.amount
        tick.net_volume = tick.volume - pre_tick.volume
        # 涨停
        if tick.bid_price_1 and not tick.ask_price_1:
            tick.direction = TickDirection.SELL
        # 跌停
        elif tick.ask_price_1 and not tick.bid_price_1:
            tick.direction = TickDirection.BUY
        # 成交价格突破前tick数据的卖一（主动买）
        elif tick.last_price > pre_tick.ask_price_1:
            tick.direction = TickDirection.BUY
        # 成交价格突破前tick数据的买一（主动卖）
        elif tick.last_price < pre_tick.bid_price_1:
            tick.direction = TickDirection.SELL
        else:
            # 买盘上移（被动买）
            if tick.ask_price_1 > pre_tick.ask_price_1:
                tick.direction = TickDirection.BUY
            # 买盘下移（被动卖）
            elif tick.bid_price_1 < pre_tick.bid_price_1:
                tick.direction = TickDirection.SELL
            else:
                tick.direction = TickDirection.OTHER
        
        return 

    def on_tick(self, data: dict):
        if not self._active:
            return
        ticks : list[TickData] = []
        for vt_symbol, d in data.items():
            symbol, exchange = vt_symbol.split(".")
            timestamp=d["time"]
            bp_data: list = d["bidPrice"]
            ap_data: list = d["askPrice"]
            bv_data: list = d["bidVol"]
            av_data: list = d["askVol"]
            tick: TickData = TickData(
                    symbol=symbol,
                    exchange=exchange,
                    datetime=generate_datetime(timestamp),
                    timestamp=timestamp,
                    last_price=round(d["lastPrice"], 3),
                    open=round(d["open"], 3),
                    high=round(d["high"], 3),
                    low=round(d["low"], 3),
                    pre_close=round(d["lastClose"], 3),
                    volume=d["volume"],
                    amount=d["amount"],
                    bid_price_1=round(bp_data[0], 3),
                    bid_price_2=round(bp_data[1], 3),
                    bid_price_3=round(bp_data[2], 3),
                    bid_price_4=round(bp_data[3], 3),
                    bid_price_5=round(bp_data[4], 3),
                    ask_price_1=round(ap_data[0], 3),
                    ask_price_2=round(ap_data[1], 3),
                    ask_price_3=round(ap_data[2], 3),
                    ask_price_4=round(ap_data[3], 3),
                    ask_price_5=round(ap_data[4], 3),
                    bid_volume_1=bv_data[0],
                    bid_volume_2=bv_data[1],
                    bid_volume_3=bv_data[2],
                    bid_volume_4=bv_data[3],
                    bid_volume_5=bv_data[4],
                    ask_volume_1=av_data[0],
                    ask_volume_2=av_data[1],
                    ask_volume_3=av_data[2],
                    ask_volume_4=av_data[3],
                    ask_volume_5=av_data[4],
                )
            self.extarp_tick(tick)
            ticks.append(tick)
            self.full_tick[vt_symbol] = tick
        self.recoder_tick_queue.put(ticks)
        self.other_tick_queue.put(ticks)
        # self.db_client.save_tick_data(ticks=ticks)

    def subscribe_whole_quote(self):
        stock_list  = self.get_stock_list()
        xtdata.subscribe_whole_quote(
            code_list=stock_list,
            callback=self.on_tick
        )

    def strart(self):
        self.subscribe_whole_quote()
        time.sleep(10)
        self._active = True
        for t in self.thread_task:
            t.start()
        print("数据录制中.....")
        while self._active:
            time.sleep(3)

    def test_strart(self):
        self._active = True
        for t in self.thread_task:
            t.start()
        lst = self.db_client.load_tick_data(
            "300575.SZ",
            datetime.datetime(year=2025, month=7, day=25, hour=9, minute=10),
            datetime.datetime(year=2025, month=7, day=25, hour=15, minute=10),
        )
        for tick in lst:
            self.extarp_tick(tick)
            self.full_tick[tick.vt_symbol] = tick
            self.recoder_tick_queue.put([tick])
            self.other_tick_queue.put([tick])
        time.sleep(10)
        self.close()


    def close(self):
        self._active = False
        for t in self.thread_task:
            t.join()

        self.db_client.close()

if __name__ == "__main__":
    qmt_server = RecodeServer()
    qmt_server.strart()
    # qmt_server.test_strart()



