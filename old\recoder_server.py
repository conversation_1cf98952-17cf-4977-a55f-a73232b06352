import time
from datetime import datetime
from queue import Empty, Queue
from threading import Thread
from tdengine_xz.taosws_database import TaoswsDatabase
from xtquant import xtdata
from vnpy_xz.trader.object import TickData, BarData
from vnpy_xz.trader.utility import BarGenerator, extract_vt_symbol
from vnpy_xz.trader.constant import Exchange, Interval

class RecodeServer:
    def __init__(self):
        self._active = False
        self.recoder_tick_queue = Queue()
        self.other_tick_queue = Queue()
        self.vt_symbols: list[str] = []
        self.bgs: dict[str, BarGenerator] = {}
        self.last_tick_time: datetime | None = None

        self.db_client = TaoswsDatabase()
        # self.init()
  

    def init(self):
        self.db_client.init_db()
        self.vt_symbols = self.get_stock_list()
        # 绑定合约代码
        for vt_symbol in self.vt_symbols:
            def on_bar(bar: BarData) -> None:
                """"""
                pass
            self.bgs[vt_symbol] = BarGenerator(on_bar)
    
    def strart(self):
        # self._active = True
        self.subscribe_whole_quote()
        recoder_tick: Thread = Thread(target=self.recoder_tick)
        handle_tick: Thread = Thread(target=self.handle_tick)
        recoder_tick.start()
        handle_tick.start()
        # t1 = time.time()
        # start_time = "2025-0725-9:15"
        # end_time = "2025-0725-9:30"
        # start_time = datetime.strptime(start_time, "%Y-%m%d-%H:%M")
        # end_time = datetime.strptime(end_time, "%Y-%m%d-%H:%M")
        # d = self.db_client.load_tick_data(
        #     "300575.SZ",
        #     start_time,
        #     end_time,

        # )
        # t2 = time.time()
        # print(t2-t1, len(d))
        while self._active:
            time.sleep(3)


    def get_stock_list(self):
        lst = [
            "沪深A股",
            "沪深ETF",
            # "沪深基金",
            # "沪深指数",
            "沪深转债",
        ]
        stock_list = []
        for k in lst:
            stock_list.extend(xtdata.get_stock_list_in_sector(k)) 
        return stock_list

    def recoder_tick(self):
        while self._active:
            try:
                ticks = self.recoder_tick_queue.get(block=True, timeout=1)
                self.db_client.save_tick_data(ticks=ticks)
            except Empty:
                pass
    
    def generate_datetime(self, timestamp: int, millisecond: bool = True) -> datetime:
        """生成本地时间"""
        if millisecond:
            dt: datetime = datetime.fromtimestamp(timestamp / 1000)
        else:
            dt = datetime.fromtimestamp(timestamp)
        return dt
    
    def handle_tick(self):
        tick: TickData = None
        while self._active:
            try:
                ticks = self.other_tick_queue.get(block=True, timeout=1)
                # 合成分钟数据
                for tick in ticks:
                    if (
                        self.last_tick_time
                        and self.last_tick_time.minute != tick.datetime.minute
                    ):
                        bars = {}
                        for vt_symbol, bg in self.bgs.items():
                            bars[vt_symbol] = bg.generate()
                        # self.on_bars(bars)
                        # print("bars", bars)
                        self.db_client.save_bar_data(bars=[bar for bar in bars.values() if bar], interval=Interval.MINUTE)

                    bg = self.bgs[tick.vt_symbol]
                    bg.update_tick(tick)

                    self.last_tick_time = tick.datetime
            except Empty:
                pass
    

    def on_tick(self, data: dict):
        if not self._active:
            return
        ticks : list[TickData] = []
        for vt_symbol, d in data.items():
            symbol, exchange = vt_symbol.split(".")
            timestamp=d["time"]
            bp_data: list = d["bidPrice"]
            ap_data: list = d["askPrice"]
            bv_data: list = d["bidVol"]
            av_data: list = d["askVol"]
            tick: TickData = TickData(
                    symbol=symbol,
                    exchange=exchange,
                    datetime=self.generate_datetime(timestamp),
                    timestamp=timestamp,
                    last_price=d["lastPrice"],
                    open=d["open"],
                    high=d["high"],
                    low=d["low"],
                    pre_close=d["lastPrice"],
                    volume=d["volume"],
                    amount=d["amount"],
                    bid_price_1=bp_data[0],
                    bid_price_2=bp_data[1],
                    bid_price_3=bp_data[2],
                    bid_price_4=bp_data[3],
                    bid_price_5=bp_data[4],
                    ask_price_1=ap_data[0],
                    ask_price_2=ap_data[1],
                    ask_price_3=ap_data[2],
                    ask_price_4=ap_data[3],
                    ask_price_5=ap_data[4],
                    bid_volume_1=bv_data[0],
                    bid_volume_2=bv_data[1],
                    bid_volume_3=bv_data[2],
                    bid_volume_4=bv_data[3],
                    bid_volume_5=bv_data[4],
                    ask_volume_1=av_data[0],
                    ask_volume_2=av_data[1],
                    ask_volume_3=av_data[2],
                    ask_volume_4=av_data[3],
                    ask_volume_5=av_data[4],
                )
            ticks.append(tick)
        self.recoder_tick_queue.put(ticks)
        self.other_tick_queue.put(ticks)


    def subscribe_whole_quote(self):
        stock_list  = self.get_stock_list()
        xtdata.subscribe_whole_quote(
            code_list=stock_list,
            callback=self.on_tick
        )
        time.sleep(10)
        self._active = True


if __name__ == "__main__":
    qmt_server = RecodeServer()
    qmt_server.db_client.stmt2_query()
    # qmt_server.strart()


