import time
import datetime
import taosws
from xtquant import xtdata
from tdengine_client import TDClient
import pandas as pd

from concurrent.futures import ThreadPoolExecutor, as_completed
env = {
    'db_protocol': 'taosws',
    'db_user': "root",
    'db_pass': "taosdata",
    'db_host': "**************",
    'db_port': 6041,
    'db_name': "histor",
}

class QmtServer:
    def __init__(self):
        self.sql_clinet = TDClient(env)
        self._active = False
        self.db_name = "real_data"
        self.tick_table = "tick"
        self.tick_keys = ["datatime",
                        #   "stock_code",
                "last_price", "open", "high", "low","pre_close", "volume", "amount",
                "bid_price_1", "bid_price_2", "bid_price_3", "bid_price_4", "bid_price_5",
                "ask_price_1", "ask_price_2", "ask_price_3", "ask_price_4","ask_price_5",
                "bid_volume_1", "bid_volume_2", "bid_volume_3", "bid_volume_4", "bid_volume_5",
                "ask_volume_1", "ask_volume_2", "ask_volume_3", "ask_volume_4", "ask_volume_5",
                "local_time",
                ]
        self.executor_pool = ThreadPoolExecutor(max_workers=10)
        self.stock_list = None
        self.tick_conn = None
        self.tick_stmt2 = None
    
    def on_init(self):
        self.stock_list = self.get_stock_list()
        self.create_db()
        self.create_table()
        
    
    def create_db(self):
        sql = f"CREATE DATABASE IF NOT EXISTS {self.db_name} KEEP 3650"
        self.sql_clinet.conn_execute(sql)

    def create_table(self):
        columns = {}
        for x in self.tick_keys:
            if x == "datatime":
                columns[x] = "TIMESTAMP"
            elif x == "stock_code":
                columns[x] = "BINARY(20) COMPOSITE KEY"
            elif x == "local_time":
                columns[x] = "TIMESTAMP"
            else:
                columns[x] = "DOUBLE"
        tags = {
            "stock_code": "BINARY(20)",
            "code": "BINARY(20)",
            "market": "BINARY(20)",
        }
        self.sql_clinet.create_table(self.tick_table, columns=columns, tags=tags, db_name=self.db_name)
    

    def init_stem2_tick(self):
        sql = f"insert into ? using tick tags (?, ?, ?) values ({','.join(['?' for _ in self.tick_keys])})"
        self.tick_conn = self.sql_clinet.create_connect()
        self.tick_conn.execute(f"use {self.db_name}")
        self.tick_stmt2 = self.tick_conn.stmt2_statement()
        self.tick_stmt2.prepare(sql) 
  
    
    def on_tick(self, data: dict):
        c = 0
        stmt2param_lst= []
        for stock_code, dct in data.items():
            code, market =  stock_code.split(".")
            # c += 1
            bid_p_lst = dct["bidPrice"]
            ask_p_lst = dct["askPrice"]
            bid_vol = dct["bidVol"]
            ask_vol = dct["askVol"]
            n_dct = {
                "datatime": dct["time"],
                "last_price": dct["lastPrice"],
                "open": dct["open"],
                "high": dct["high"],
                "low": dct["low"],
                "pre_close": dct["lastClose"],
                "volume": dct["volume"],
                "amount": dct["amount"],
                "bid_price_1": bid_p_lst[0],
                "bid_price_2": bid_p_lst[1],
                "bid_price_3": bid_p_lst[2],
                "bid_price_4": bid_p_lst[3],
                "bid_price_5": bid_p_lst[4],
                "ask_price_1": ask_p_lst[0],
                "ask_price_2": ask_p_lst[1],
                "ask_price_3": ask_p_lst[2],
                "ask_price_4": ask_p_lst[3],
                "ask_price_5": ask_p_lst[4],
                "bid_volume_1": bid_vol[0],
                "bid_volume_2": bid_vol[1],
                "bid_volume_3": bid_vol[2],
                "bid_volume_4": bid_vol[3],
                "bid_volume_5": bid_vol[4],
                "ask_volume_1": ask_vol[0],
                "ask_volume_2": ask_vol[1],
                "ask_volume_3": ask_vol[2],
                "ask_volume_4": ask_vol[3],
                "ask_volume_5": ask_vol[4],
            }
            columns = []
            ts = int(time.time()*1000)
            for k in self.tick_keys:
                if k=='datatime':
                    columns.append(
                    taosws.millis_timestamps_to_column([n_dct[k]])   
                    )
                elif k=='local_time':
                    columns.append(
                    taosws.millis_timestamps_to_column([ts])   
                    )
                else:
                    columns.append(
                    taosws.doubles_to_column([n_dct[k]])   
                    )

            stmt2param = taosws.stmt2_bind_param_view(
                table_name=f"tick_{code}_{market.lower()}", 
                tags=[
                     taosws.varchar_to_tag(stock_code),
                     taosws.varchar_to_tag(code),
                     taosws.varchar_to_tag(market),
                ],
                columns=columns
            )
            stmt2param_lst.append(stmt2param)

        self.tick_stmt2.bind(stmt2param_lst)
        c = self.tick_stmt2.execute()
        # t2 = time.time()
        # print(f"时间：{t2 - t1},数量：{c}")
    
    def start_recode_tick(self):
        # self._active = True
        self.init_stem2_tick()
        xtdata.subscribe_whole_quote(
            code_list=self.stock_list,
            callback=self.on_tick
        )
        xtdata.run()

    def get_stock_list(self):
        lst = [
            "沪深A股",
            "沪深ETF",
            # "沪深基金",
            # "沪深指数",
            "沪深转债",
        ]
        stock_list = []
        for k in lst:
            stock_list.extend(xtdata.get_stock_list_in_sector(k)) 
        return stock_list

    def real_to_history(self, stock_code, period):
            t1 = time.time()
            time_0900 = datetime.datetime.now().replace(hour=9, minute=0, second=0, microsecond=0).timestamp()*1000
            # time_0900 = time_0900 - 60*60*24*1000
            table_name = f"real_data.{period}"
            # sql = f"select * from {table_name} where stock_code='{stock_code}'"
            sql = f"select * from {table_name} where stock_code='{stock_code}' and datatime > {time_0900}"
            t2 = time.time()
            d = self.sql_clinet.query(sql)
            dct = {}
            for x in d:
                v = x[0]
                dt = datetime.datetime.strptime(v, '%Y-%m-%d %H:%M:%S %z')
                datatime = int(dt.timestamp()*1000)
                # if datatime < time_0900:
                #     continue
                for idx, k in enumerate(self.tick_keys):
                
                # for k, v in x.items():
                    if k in ("stock_code", "local_time"):
                        continue
                    if k == "datatime":
                        v = datatime
                    else:
                        v = x[idx]
                    dct.setdefault(k, []).append(v)
            
            # d = self.sql_clinet.fetchall(sql, is_dict=True)
            t3 = time.time()
            # df = pd.DataFrame(d)
            # df.drop(columns=['stock_code', 'local_time'], inplace=True)
            # df["datatime"] = df["datatime"].astype("int64") // 1_000_000
            # t4 = time.time()
            c = self.sql_clinet.stmt2_insert_histor(
                stock_code,
                dct,
                period=period,
                db_name='history'
            )
            t4 = time.time()
            print(f"stock_code: {stock_code}; 数量： {c}; 时间： {t2 - t1}、{t3 - t2}、 {t4-t3}")

    def real_to_history_tick(self, period='tick'):
        # self.sql_clinet.init_history_stable()
        # self.sql_clinet.create_history_tables(
        #     code_list=self.stock_list,
        #     period='tick',
        #     db_name='history',
        # )
        # table_name = f"real_data.{period}"
        # time_0900 = datetime.datetime.now().replace(hour=9, minute=0, second=0, microsecond=0).timestamp()*1000
        
        # futures = []
        s = "601515.SH"
        a = False
        for stock_code in self.stock_list:
            if stock_code == s:
                a = True
            if not a:
                continue
            self.real_to_history(
                stock_code,
                period=period,
            )

        #     f = self.executor_pool.submit(
        #         self.real_to_history,
        #         stock_code,
        #         period=period,
        #     )
        #     futures.append(f)
        # for future in as_completed(futures):
        #     try:
        #         result = future.result()
        #         # print("任务完成，结果:", result)
        #     except Exception as e:
        #         print("任务出错:", e)

if __name__ == "__main__":
    qmt_server = QmtServer()
    # sql = f"DROP database IF EXISTS real_data"
    # qmt_server.sql_clinet.conn_execute(sql)

    # qmt_server.init_stem2_tick2()
    # qmt_server.on_init()
    # qmt_server.start_recode_tick()
    # qmt_server.real_to_history_tick()
    # time_0900 = datetime.datetime.now().replace(hour=9, minute=0, second=0, microsecond=10).timestamp()*1000
    # print(type(time_0900), time_0900)
    # # time_0900 = int(time_0900)
    sql = f"select * from real_data.tick where stock_code='513560.SH'"
    # print(sql)
    d = qmt_server.sql_clinet.fetchone(sql, is_dict=True)
    print(d)
    print(type(d[0]["datatime"]))
    # qmt_server.sql_clinet.drop_db("real_data")
    # qmt_server.sql_clinet.drop_table("history.tick", is_super=True)

    t2 = time.time()
    # print(d)
    # df = pd.DataFrame(d)
    # df.to_csv('output3.csv') 
    # print(len(d), t2- t1)




