#!/usr/bin/env python3
"""
高效的 ZeroMQ 工具 - 单文件实现
效率第一，简单易用，便于移植

依赖: pip install pyzmq ujson

使用示例:
    # 服务端
    server = ZMQServer("tcp://*:5555")
    server.start(lambda msg: {"echo": msg})
    
    # 客户端  
    client = ZMQClient("tcp://localhost:5555")
    response = client.request({"hello": "world"})
"""

import zmq
import ujson
import threading
import time
import logging
from typing import Any, Callable, Optional, Dict, List
from concurrent.futures import ThreadPoolExecutor


class ZMQServer:
    """高效的 ZeroMQ 服务端"""
    
    def __init__(self, address: str = "tcp://*:5555", workers: int = 10):
        self.address = address
        self.workers = workers
        self.context = zmq.Context()
        self.socket = None
        self.running = False
        self.handler = None
        self.executor = None
        self.stats = {"received": 0, "sent": 0, "errors": 0}
        
    def start(self, handler: Callable[[Any], Any], blocking: bool = True):
        """启动服务端"""
        self.handler = handler
        self.socket = self.context.socket(zmq.REP)
        
        # 高性能配置
        self.socket.setsockopt(zmq.LINGER, 0)
        self.socket.setsockopt(zmq.RCVHWM, 10000)
        self.socket.setsockopt(zmq.SNDHWM, 10000)
        self.socket.setsockopt(zmq.RCVTIMEO, 5000)
        self.socket.setsockopt(zmq.SNDTIMEO, 5000)
        
        self.socket.bind(self.address)
        self.running = True
        self.executor = ThreadPoolExecutor(max_workers=self.workers)
        
        print(f"ZMQ Server started on {self.address}")
        
        if blocking:
            self._run()
        else:
            threading.Thread(target=self._run, daemon=True).start()
    
    def _run(self):
        """运行服务端主循环"""
        while self.running:
            try:
                # 接收消息
                raw_msg = self.socket.recv(zmq.NOBLOCK)
                self.stats["received"] += 1
                
                # 异步处理
                self.executor.submit(self._handle_message, raw_msg)
                
            except zmq.Again:
                time.sleep(0.001)
            except Exception as e:
                self.stats["errors"] += 1
                print(f"Server error: {e}")
    
    def _handle_message(self, raw_msg: bytes):
        """处理单个消息"""
        try:
            # 反序列化
            msg = ujson.loads(raw_msg.decode('utf-8'))
            
            # 调用处理器
            response = self.handler(msg) if self.handler else {"echo": msg}
            
            # 序列化并发送响应
            response_data = ujson.dumps(response).encode('utf-8')
            self.socket.send(response_data)
            self.stats["sent"] += 1
            
        except Exception as e:
            # 发送错误响应
            error_response = ujson.dumps({"error": str(e)}).encode('utf-8')
            try:
                self.socket.send(error_response)
            except:
                pass
            self.stats["errors"] += 1
    
    def stop(self):
        """停止服务端"""
        self.running = False
        if self.executor:
            self.executor.shutdown(wait=True)
        if self.socket:
            self.socket.close()
        print("ZMQ Server stopped")
    
    def get_stats(self) -> Dict[str, int]:
        """获取统计信息"""
        return self.stats.copy()


class ZMQClient:
    """高效的 ZeroMQ 客户端"""
    
    def __init__(self, address: str = "tcp://localhost:5555", timeout: int = 5000):
        self.address = address
        self.timeout = timeout
        self.context = zmq.Context()
        self.socket = None
        self.stats = {"sent": 0, "received": 0, "errors": 0}
        self._connect()
    
    def _connect(self):
        """连接到服务端"""
        self.socket = self.context.socket(zmq.REQ)
        
        # 高性能配置
        self.socket.setsockopt(zmq.LINGER, 0)
        self.socket.setsockopt(zmq.RCVTIMEO, self.timeout)
        self.socket.setsockopt(zmq.SNDTIMEO, self.timeout)
        
        self.socket.connect(self.address)
        print(f"ZMQ Client connected to {self.address}")
    
    def request(self, data: Any) -> Any:
        """发送请求并获取响应"""
        try:
            # 序列化并发送
            request_data = ujson.dumps(data).encode('utf-8')
            self.socket.send(request_data)
            self.stats["sent"] += 1
            
            # 接收响应
            response_data = self.socket.recv()
            self.stats["received"] += 1
            
            # 反序列化
            return ujson.loads(response_data.decode('utf-8'))
            
        except Exception as e:
            self.stats["errors"] += 1
            raise Exception(f"Request failed: {e}")
    
    def close(self):
        """关闭客户端"""
        if self.socket:
            self.socket.close()
        print("ZMQ Client closed")
    
    def get_stats(self) -> Dict[str, int]:
        """获取统计信息"""
        return self.stats.copy()


class ZMQPublisher:
    """高效的 ZeroMQ 发布者"""
    
    def __init__(self, address: str = "tcp://*:5556"):
        self.address = address
        self.context = zmq.Context()
        self.socket = self.context.socket(zmq.PUB)
        
        # 高性能配置
        self.socket.setsockopt(zmq.LINGER, 0)
        self.socket.setsockopt(zmq.SNDHWM, 10000)
        
        self.socket.bind(address)
        self.stats = {"published": 0, "errors": 0}
        print(f"ZMQ Publisher started on {address}")
    
    def publish(self, topic: str, data: Any):
        """发布消息"""
        try:
            topic_bytes = topic.encode('utf-8')
            data_bytes = ujson.dumps(data).encode('utf-8')
            self.socket.send_multipart([topic_bytes, data_bytes])
            self.stats["published"] += 1
        except Exception as e:
            self.stats["errors"] += 1
            print(f"Publish error: {e}")
    
    def close(self):
        """关闭发布者"""
        self.socket.close()
        print("ZMQ Publisher closed")
    
    def get_stats(self) -> Dict[str, int]:
        return self.stats.copy()


class ZMQSubscriber:
    """高效的 ZeroMQ 订阅者"""
    
    def __init__(self, address: str = "tcp://localhost:5556"):
        self.address = address
        self.context = zmq.Context()
        self.socket = self.context.socket(zmq.SUB)
        self.socket.connect(address)
        self.running = False
        self.handler = None
        self.stats = {"received": 0, "errors": 0}
        print(f"ZMQ Subscriber connected to {address}")
    
    def subscribe(self, topic: str = ""):
        """订阅主题"""
        self.socket.setsockopt_string(zmq.SUBSCRIBE, topic)
        print(f"Subscribed to topic: '{topic}'")
    
    def start(self, handler: Callable[[str, Any], None], blocking: bool = True):
        """开始监听"""
        self.handler = handler
        self.running = True
        
        if blocking:
            self._listen()
        else:
            threading.Thread(target=self._listen, daemon=True).start()
    
    def _listen(self):
        """监听消息"""
        while self.running:
            try:
                parts = self.socket.recv_multipart(zmq.NOBLOCK)
                if len(parts) >= 2:
                    topic = parts[0].decode('utf-8')
                    data = ujson.loads(parts[1].decode('utf-8'))
                    self.stats["received"] += 1
                    
                    if self.handler:
                        self.handler(topic, data)
                        
            except zmq.Again:
                time.sleep(0.001)
            except Exception as e:
                self.stats["errors"] += 1
                print(f"Subscriber error: {e}")
    
    def stop(self):
        """停止监听"""
        self.running = False
        print("ZMQ Subscriber stopped")
    
    def close(self):
        """关闭订阅者"""
        self.stop()
        self.socket.close()
        print("ZMQ Subscriber closed")
    
    def get_stats(self) -> Dict[str, int]:
        return self.stats.copy()


class ZMQAsyncClient:
    """异步 ZeroMQ 客户端，支持并发请求"""
    
    def __init__(self, address: str = "tcp://localhost:5555", workers: int = 10):
        self.address = address
        self.workers = workers
        self.context = zmq.Context()
        self.executor = ThreadPoolExecutor(max_workers=workers)
        self.stats = {"sent": 0, "received": 0, "errors": 0}
        print(f"ZMQ Async Client ready for {address}")
    
    def request_async(self, data: Any):
        """异步发送请求"""
        return self.executor.submit(self._single_request, data)
    
    def _single_request(self, data: Any) -> Any:
        """单个请求处理"""
        socket = self.context.socket(zmq.REQ)
        socket.setsockopt(zmq.LINGER, 0)
        socket.setsockopt(zmq.RCVTIMEO, 5000)
        socket.setsockopt(zmq.SNDTIMEO, 5000)
        
        try:
            socket.connect(self.address)
            
            # 发送请求
            request_data = ujson.dumps(data).encode('utf-8')
            socket.send(request_data)
            self.stats["sent"] += 1
            
            # 接收响应
            response_data = socket.recv()
            self.stats["received"] += 1
            
            return ujson.loads(response_data.decode('utf-8'))
            
        except Exception as e:
            self.stats["errors"] += 1
            raise Exception(f"Async request failed: {e}")
        finally:
            socket.close()
    
    def close(self):
        """关闭异步客户端"""
        self.executor.shutdown(wait=True)
        print("ZMQ Async Client closed")
    
    def get_stats(self) -> Dict[str, int]:
        return self.stats.copy()


# 使用示例
if __name__ == "__main__":
    import time
    
    # REQ/REP 示例
    def demo_req_rep():
        print("=== REQ/REP Demo ===")
        
        # 启动服务端
        server = ZMQServer("tcp://*:5555")
        server.start(lambda msg: {"echo": msg, "timestamp": time.time()}, blocking=False)
        
        time.sleep(0.5)  # 等待服务端启动
        
        # 客户端请求
        client = ZMQClient("tcp://localhost:5555")
        
        for i in range(3):
            response = client.request({"id": i, "message": f"Hello {i}"})
            print(f"Response: {response}")
        
        print(f"Server stats: {server.get_stats()}")
        print(f"Client stats: {client.get_stats()}")
        
        client.close()
        server.stop()
    
    # PUB/SUB 示例
    def demo_pub_sub():
        print("\n=== PUB/SUB Demo ===")
        
        # 启动发布者
        publisher = ZMQPublisher("tcp://*:5556")
        
        # 启动订阅者
        subscriber = ZMQSubscriber("tcp://localhost:5556")
        subscriber.subscribe("news")
        subscriber.start(lambda topic, data: print(f"Got {topic}: {data}"), blocking=False)
        
        time.sleep(0.5)  # 等待连接建立
        
        # 发布消息
        for i in range(3):
            publisher.publish("news", {"headline": f"News {i}", "content": "Important"})
            time.sleep(0.2)
        
        time.sleep(1)  # 等待消息处理
        
        print(f"Publisher stats: {publisher.get_stats()}")
        print(f"Subscriber stats: {subscriber.get_stats()}")
        
        subscriber.close()
        publisher.close()
    
    # 异步客户端示例
    def demo_async():
        print("\n=== Async Demo ===")
        
        # 启动服务端
        server = ZMQServer("tcp://*:5557")
        server.start(lambda msg: {"processed": msg["id"]}, blocking=False)
        
        time.sleep(0.5)
        
        # 异步客户端
        async_client = ZMQAsyncClient("tcp://localhost:5557", workers=5)
        
        # 并发发送请求
        futures = []
        start_time = time.time()
        
        for i in range(10):
            future = async_client.request_async({"id": i, "data": f"task_{i}"})
            futures.append(future)
        
        # 获取结果
        for i, future in enumerate(futures):
            result = future.result()
            print(f"Async result {i}: {result}")
        
        end_time = time.time()
        print(f"Processed 10 requests in {end_time - start_time:.2f} seconds")
        print(f"Async client stats: {async_client.get_stats()}")
        
        async_client.close()
        server.stop()
    
    try:
        demo_req_rep()
        demo_pub_sub()
        demo_async()
    except KeyboardInterrupt:
        print("\nDemo interrupted")
    except Exception as e:
        print(f"Demo error: {e}")
