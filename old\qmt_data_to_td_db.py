from xtquant import xtdata
from utils.tdengine_client import TDClient
import numpy as np
import time
from queue import Empty, Queue
from threading import Thread    
import time
import datetime

class QmtToTd:
    def __init__(self):
        self.full_tick = {}
        self.fa = False
        self._queue: Queue = Queue()
        self.mine_queue: Queue = Queue()
        self._active: bool = False
        self.last_tm = None # 最新分钟级别时间戳
        self.env = {
            'db_protocol': 'taosws',
            'db_user': "root",
            'db_pass': "taosdata",
            'db_host': "**************",
            'db_port': 6041,
            'db_name': "history",
        }
        self.td_client = TDClient(self.env)
        self.db_name = self.env['db_name']
        
    
    def create_db_table(self):
        self.td_client.create_db(self.db_name)
        tags={
                "stock_code ": "NCHAR(20)",
                "code": "NCHAR(10)",
                "market": "NCHAR(10)",
            }
        columns={
                "datetime": "TIMESTAMP",
                "open": "DOUBLE",
                "high": "DOUBLE",
                "low": "DOUBLE",
                "close": "DOUBLE",
                "pre_close": "DOUBLE",
                "volume": "DOUBLE",
                "amount": "DOUBLE",
            }
        self.td_client.create_table(
            table_name="day",
            columns=columns,
            tags=tags,
            db_name=self.db_name
        )
        self.td_client.create_table(
            table_name="minute",
            columns=columns,
            tags=tags,
            db_name=self.db_name
        )
        



    def sector_to_td(
            self,
            sector_name="沪深A股", 
            period='1m', 
            start_time="", 
            end_time="",
            dividend_type="none"
            ):
        lst = [
            # "沪深A股",
            "沪深ETF",
            "沪深基金",
            "沪深指数",
            "沪深转债",
        ]
        code_list = []
        for k in lst:
            code_list.extend(xtdata.get_stock_list_in_sector(k))  
        self.td_client.create_history_tables(code_list, period=period)
        for stock_code in code_list:
            dct = xtdata.get_market_data_ex(
                stock_list=[stock_code],
                period=period,
                # count=5000,
                start_time=start_time,
                end_time=end_time,
                dividend_type=dividend_type,
            )
            df = dct[stock_code]
            df['datetime'] = df["time"]
            # df['datetime'] = df["time"].astype(str).str.zfill(13)
            # df['open'] = df["open"].round(3)
            # df['high'] = df["high"].round(3)
            # df['low'] = df["low"].round(3)
            # df['close'] = df["close"].round(3)
            df['pre_close'] = df["preClose"]
            # df['amount'] = df["amount"]
            new_order = ['datetime', 'open', 'high', 'low', 'close', "pre_close", 'volume', "amount"]
            df = df[new_order]
            df = df.fillna(0.0)
            self.td_client.stmt_insert(stock_code=stock_code,df=df,period=period)
            # self.td_client.insert_data(stock_code=stock_code,data_lst=df.values.tolist(),period=period)

    def merger_mine(self):
        try:
            tm = self._queue.get(timeout=0.01)

        except Exception:
            pass


    def recode_tick(self):
        while self._active:
            try:
                d_lst = self._queue.get(timeout=0.01)
                taos_db.insert_data(data={"normal_full": d_lst}, period="tick")
            except Empty:
                    pass
 
    def subscribe_whole_quote(self, sector_name="沪深A股"):
        code_list = xtdata.get_stock_list_in_sector(sector_name)
        self._active = True
        self.t = Thread(target=self.recode_tick)
        self.t.start()
        # tm9 = datetime.time(9, 0, 0) // 60000
        # tm15 = datetime.time(15, 0, 0) // 60000

        # 获取今天 9:00:00 的时间戳并也对齐
        today = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        target_time = today.replace(hour=9)
        target_timestamp_ms = int(target_time.timestamp() * 1000)
        tm9 = target_timestamp_ms // 60000
        target_time = today.replace(hour=15)
        target_timestamp_ms = int(target_time.timestamp() * 1000)
        tm15 = target_timestamp_ms // 60000

        def on_data(data):
            c = 0
            d_lst = []
            for k, v in data.items():
                timestamp = v.get("time", 0)
                if c == 0:
                    tm = timestamp // 60000
                    if tm < tm9 or tm > tm15:
                        return
                    if tm != self.last_tm:
                        self.mine_queue.put(self.last_tm)
                        self.last_tm = tm
                c += 1
                stock_dct = self.full_tick.get(k, {})
                ask_price = v.get("askPrice", [0, 0, 0, 0, 0])
                bid_price = v.get("bidPrice", [0, 0, 0, 0, 0])
                ask_vol = v.get("askVol", [0, 0, 0, 0, 0])
                bid_vol = v.get("bidVol", [0, 0, 0, 0, 0])
                lst  = [
                    timestamp+c,
                    k,
                    round(v.get("open", 0), 3),
                    round(v.get("high", 0), 3),
                    round(v.get("low", 0), 3),
                    round(v.get("lastPrice", 0), 3),
                    round(v.get("lastClose", 0), 3),
                    v.get("volume", 0),
                    v.get("amount", 0),
                    ask_price[0],
                    ask_price[1],
                    ask_price[2],
                    ask_price[3],
                    ask_price[4],
                    bid_price[0],
                    bid_price[1],
                    bid_price[2],
                    bid_price[3],
                    bid_price[4],
                    ask_vol[0],
                    ask_vol[1],
                    ask_vol[2],
                    ask_vol[3],
                    ask_vol[4],
                    bid_vol[0],
                    bid_vol[1],
                    bid_vol[2],
                    bid_vol[3],
                    bid_vol[4],
                    v.get("transactionNum", 0),    
                ]
                self.full_tick[k] = v
                d_lst.append(lst)
            self._queue.put(d_lst)
       
        xtdata.subscribe_whole_quote(
            code_list=code_list,
            # code_list=['SH', 'SZ'],
            callback=on_data,
        )

        while self._active:
            c = int(datetime.datetime.now().timestamp() * 1000)
            current_time = c // 60000
            if current_time > tm15 + 10:
                self._active = False
                break
            time.sleep(60 * 10)
    
    
    def del_test_db(self):
        taos_db.drop_database(database_name="test_stock_data", confirm=True)

if __name__ == "__main__":
    qmt_td = QmtToTd()
    # qmt_td.create_db_table()
    qmt_td.sector_to_td(period="1m")
    # qmt_td.subscribe_whole_quote()
    # start_time = "20250701"
    # dt = datetime.datetime.strptime(start_time, "%Y%m%d")
    # # 转换为时间戳（单位：秒）
    # timestamp_seconds = int(dt.timestamp())

    # # 如果你需要毫秒级时间戳：
    # start_time = int(dt.timestamp() * 1000)
    # sql = f"select * from tick_normal_full where stock_code='000001.SH'"
    # d = taos_db.query(sql)
    # # d.to_csv('test003.csv')
    # print(d)

    # taos_db.drop_table("tick_normal", confirm=True)
    # taos_db.create_tick_normal_table(table_name="tick_normal_full")
    # qmt_td.sector_to_td(period="1m", start_time=start_time)
    # qmt_td.recode_to_td()
    # qmt_td.del_test_db()

