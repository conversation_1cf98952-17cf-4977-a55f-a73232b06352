#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from queue import Queue, Empty
from typing import Dict, List, Any, Optional, Union
from contextlib import contextmanager
import taosws
import logging
import pandas as pd
import datetime
import warnings
#  忽略 pandas 关于 SQLAlchemy 的警告
warnings.filterwarnings("ignore", message=".*pandas only supports SQLAlchemy.*")
logger = logging.getLogger(__name__)

class TaoswsDatabase:
    """TDengine WebSocket 简单高效客户端"""
    
    def __init__(self, config: dict):
        """
        """
        db_protocol = config.get("db_protocol", "taosws")
        db_user = config.get("db_user", "root")
        db_pass = config.get("db_pass", "taosdata")
        db_host = config.get("db_host", "localhost")
        db_port = config.get("db_port", "6041")
        pool_size = config.get("pool_size", 10)
        self.db_name = config.get("db_name", None)
        max_workers = config.get("max_workers", 5)
        self.db_url = f"{db_protocol}://{db_user}:{db_pass}@{db_host}:{db_port}"
        if self.db_name:
            self.db_url += f"/{self.db_name}"
        self.pool_size = 0
        # 连接池
        self._pool = Queue()
        # 线程池
        self.executor_pool = ThreadPoolExecutor(max_workers=max_workers)
        # 初始化连接池
        self.add_connect_to_pool(pool_size)
    
    def create_connect(self):
        conn = taosws.connect(self.db_url)
        return conn

    def add_connect_to_pool(self, size=1):
        """连接池增加连接"""
        for _ in range(size):
            conn = self.create_connect()
            if conn:
                self._pool.put(conn)
                self.pool_size += 1
                
    @contextmanager
    def get_connection(self):
        """获取连接"""
        conn = None
        try:
            # 从池中获取连接
            conn = self._pool.get(timeout=0.1)
            yield conn
        finally:
            # 归还连接
            self._pool.put(conn, timeout=1)
        
    def cursor_execute(self, sql, db_name=None):
        with self.get_connection() as conn:
            cursor = conn.cursor()
            if db_name:
                cursor.execute("use {name}", name=db_name)
            res = cursor.execute(sql)
            cursor.close()
            return res
    
    def execute_many(self, sql, data: List[Dict]=None, db_name=None):
        with self.get_connection() as conn:
            cursor = conn.cursor()
            res = None
            if db_name:
                cursor.execute("use {name}", name=db_name)
            if data:
                res = cursor.execute_many(sql, data)
            else:
                res = cursor.execute_many(sql)
            cursor.close()
            return res
    
    def fetchone(self, sql, db_name=None, is_dict=False):
        with self.get_connection() as conn:
            cursor = conn.cursor()
            if db_name:
                cursor.execute("use {name}", name=db_name)
            cursor.execute(sql)
            if is_dict:
                row = cursor.fetch_all_into_dict()
            else:
                row = cursor.fetchone()
            cursor.close()
            return row 
        
    def fetchall(self, sql, db_name=None, is_dict=False):
        with self.get_connection() as conn:
            cursor = conn.cursor()
            if db_name:
                cursor.execute("use {name}", name=db_name)
            cursor.execute(sql)
            if is_dict:
                row = cursor.fetch_all_into_dict()
            else:
                row = cursor.fetchall()
            cursor.close()
            return row 
    
    def fetchall_async(self, sql: str, db_name=None):
        """
        """
        future = self.executor_pool.submit(self.fetchall, sql, db_name)
        # future.result()
        return future
    

    def close(self):
        """关闭客户端"""
        # 关闭线程池
        self.executor_pool.shutdown(wait=True)
        
        # 关闭所有连接
        while not self._pool.empty():
            try:
                conn = self._pool.get_nowait()
                conn.close()
            except:
                pass
        
        logger.info("TDengine 客户端已关闭")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


class TDClient(TaoswsDatabase):
    def __init__(self, config: dict):
        self.period_map = {
            "1d": "day",
            "1m": "minute",
            "1t": "tick",
            "day": "day",
            "minute": "minute",
            "tick": "tick"
        }
        super().__init__(config)
    
    def create_db(self, db_name):
        sql = f"CREATE DATABASE IF NOT EXISTS {db_name} KEEP 365000"
        res = self.cursor_execute(sql)
        logger.info(f"创建数据库 {db_name} {res}")
    
    def create_table(self, table_name: str, columns: Dict[str, str], tags: Dict[str, str] = None, db_name=None):
        """
        创建表
        Args:
            table_name: 表名
            columns: 列定义 {"列名": "类型"}
            tags: 标签定义（超级表用）
        """
        # 构建列定义
        col_defs = [f"{name} {type_}" for name, type_ in columns.items()]
        
        if tags:
            # 创建超级表
            tag_defs = [f"{name} {type_}" for name, type_ in tags.items()]
            sql = f"CREATE STABLE IF NOT EXISTS {table_name} ({','.join(col_defs)}) TAGS ({','.join(tag_defs)})"
        else:
            sql = f"CREATE TABLE IF NOT EXISTS {table_name} ({','.join(col_defs)})"
        res = self.cursor_execute(sql, db_name=db_name)
        logger.info(f"创建表 {table_name} {res}")

    def create_history_tables(self, code_list, period='1d', db_name=None):
        data = []
        period = self.period_map[period]
        for stock_code in code_list:
            code, market = stock_code.split(".")
            table_name = f"{period}_{code}_{market.lower()}"
            data.append({
                "name": table_name,
                "super_name": period,
                "code": code,
                "market": market,
                "stock_code": stock_code,
                })
            
        sql = "CREATE TABLE IF NOT EXISTS {name} USING {super_name} TAGS ('{stock_code}','{code}','{market}')"
        res = self.execute_many(sql, data=data, db_name=db_name)
        logger.info(f"创建表 {table_name} {res}")

    def insert_data(self, stock_code, data_lst: List[List], period='1d', db_name="history"):
        period = self.period_map[period]
        code, market = stock_code.split(".")
        table_name = f"{period}_{code}_{market.lower()}"
        sql = "INSERT INTO {} VALUES ('{}', {}, {}, {}, {}, {}, {}, {})"
        lst = []
        t1 = time.time()
        count = 0
        for row in data_lst:
            lst.append((table_name,) + tuple(row))
            count += 1
            if count > 5000:
                res = self.execute_many(sql, data=lst)
                lst = []
                count = 0
        res = self.execute_many(sql, data=lst)
        t2 = time.time()
        print("res", res, f"耗时: {t2 - t1:.2f}秒")
  
    def stmt_insert(self, stock_code, df: pd.DataFrame, period='1d', db_name="history"):
        t1 = time.time()
        period = self.period_map[period]
        code, market = stock_code.split(".")
        table_name = f"{period}_{code}_{market.lower()}"
        keys = df.columns.tolist()
        conn = self._pool.get()
        stmt = conn.statement()
        stmt.prepare(f"insert into {table_name} values ({','.join(['?' for _ in keys])})")
        
        batch_size = 5000
        total_rows = len(df)
        insert_count = 0
        for start in range(0, total_rows, batch_size):
            end = min(start + batch_size, total_rows)
            df_batch = df.iloc[start:end]
            bind_param = []
            for k in keys:
                if k == "datetime":
                    bind_param.append(
                        taosws.millis_timestamps_to_column(df_batch[k].tolist())
                    )
                else:
                    bind_param.append(
                        taosws.doubles_to_column(df_batch[k].tolist())
                    )
            stmt.bind_param(bind_param)
            stmt.add_batch()
            insert_count += stmt.execute()
        t2 = time.time()
        self._pool.put(conn)
        print(f"row:{total_rows}: {insert_count}, {t2 - t1}")

    def drop_table(self, table_name: str, is_super=False):
        """删除表"""
        if is_super:
            sql = f"DROP STABLE IF EXISTS {table_name}"
        else:
            sql = f"DROP TABLE IF EXISTS {table_name}"
        with self._get_connection() as conn:
            conn.execute(sql)
        logger.warning(f"删除表 {table_name}")
    
    def format_time_full(self, time_str: str) -> str:
        # 尝试匹配 "YYYY-MM-DD" 格式
        try:
            dt = datetime.datetime.strptime(time_str, "%Y-%m-%d")
            return dt.strftime("%Y-%m-%d %H:%M:00.000").replace("%H:%M", "00:00")
        except ValueError:
            pass
        # 尝试匹配 "YYYY-MM-DD HH:mm" 格式
        try:
            dt = datetime.datetime.strptime(time_str, "%Y-%m-%d %H:%M")
            return dt.strftime("%Y-%m-%d %H:%M:00.000")
        except ValueError:
            pass
        # 尝试匹配最完整的时间格式（包含毫秒）
        try:
            dt = datetime.datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S.%f")
            return dt.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]  # 只保留三位毫秒
        except ValueError:
            raise ValueError(f"Unsupported time format: {time_str}")

    def pd_read_sql(self, sql):
        with self.get_connection() as conn:
            df: pd.DataFrame = pd.read_sql(sql, conn)
            return df
        
    def pd_read_sql_async(self, sql: str):
        future = self.executor_pool.submit(self.pd_read_sql, sql)
        # future.result()
        return future

    def get_market_data_ex(self, field_list=[], stock_list=[], period='1d', start_time='', end_time='', count=-1, dividend_type='none'):
        if period not in self.period_map:
            raise ValueError(f"不支持的时间周期: {period}")
        if not stock_list:
            raise ValueError("股票列表不能为空")
        period = self.period_map[period]
        where = ""
        if field_list:
            fields = ",".join(field_list)
        else:
            fields = "*"
        if start_time:
            where += f"AND datatime >= '{self.format_time_full(start_time)}' "
        if end_time:
            where += f"AND datatime <= '{self.format_time_full(end_time)}' "
        if count > 0:
            where += f"LIMIT {count} "
        tmp_results = {}
        database = "stock_data"
        for symbol_vt in stock_list:
            table_name = f"{database}.{period}_{symbol_vt.replace('.', '_').lower()}"
            sql = f"SELECT {fields} FROM {table_name} {where}"
            tmp_results[symbol_vt] = self.pd_read_sql_async(sql)
        data = {}
        for symbol_vt, future in tmp_results.items():
            data[symbol_vt] = future.result()
        return data



if __name__ == "__main__":
    # 使用示例
    env = {
        'db_protocol': 'taosws',
        'db_user': "root",
        'db_pass': "taosdata",
        'db_host': "**************",
        'db_port': 6041,
        'db_name': "stock_data",
    }
    client = TDClient(env)
    t1 = time.time()
    # with client.get_connection() as conn:
    #     cursor = conn.cursor()
   
    #   
    sql = "SELECT * FROM day_000001_sz"
    #     cursor = conn.cursor()
    #     d = conn.query(sql)
    d =  client.query(sql, db_name="stock_data")
    # d =  client.fetchall(sql, db_name="stock_data")
    
    # d = client.get_market_data_ex(stock_list=["000001.SZ", "000002.SZ", "000003.SZ"])
    t2 = time.time()
    print(d)
    print(f"耗时: {t2 - t1:.2f}秒")
    
